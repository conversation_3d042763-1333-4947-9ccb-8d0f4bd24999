use crate::commands::plagiarism::{PlagiarismBatch, SentenceMatch, BatchStatistics};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tokio::sync::RwLock;
use chrono::Utc;

#[derive(Clone)]
pub struct BatchManager {
    batches: Arc<RwLock<HashMap<String, PlagiarismBatch>>>,
    matches: Arc<RwLock<HashMap<String, Vec<SentenceMatch>>>>,
    running_tasks: Arc<Mutex<HashMap<String, bool>>>,
}

impl BatchManager {
    pub fn new() -> Self {
        Self {
            batches: Arc::new(RwLock::new(HashMap::new())),
            matches: Arc::new(RwLock::new(HashMap::new())),
            running_tasks: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// 创建新的查重批次
    pub async fn create_batch(
        &self,
        name: String,
        description: Option<String>,
        book_ids: Vec<String>,
    ) -> String {
        let batch_id = self.generate_batch_id();
        let now = Utc::now().to_rfc3339();
        
        let batch = PlagiarismBatch {
            id: batch_id.clone(),
            name,
            description,
            book_ids,
            total_matches: 0,
            status: "pending".to_string(),
            create_time: now.clone(),
            update_time: now,
            completed_time: None,
            progress: Some(0),
        };

        let mut batches = self.batches.write().await;
        batches.insert(batch_id.clone(), batch);
        
        batch_id
    }

    /// 获取批次信息
    pub async fn get_batch(&self, batch_id: &str) -> Option<PlagiarismBatch> {
        let batches = self.batches.read().await;
        batches.get(batch_id).cloned()
    }

    /// 更新批次状态
    pub async fn update_batch_status(&self, batch_id: &str, status: &str, progress: Option<i32>) {
        let mut batches = self.batches.write().await;
        if let Some(batch) = batches.get_mut(batch_id) {
            batch.status = status.to_string();
            batch.update_time = Utc::now().to_rfc3339();
            if let Some(p) = progress {
                batch.progress = Some(p);
            }
            if status == "completed" || status == "failed" {
                batch.completed_time = Some(Utc::now().to_rfc3339());
            }
        }
    }

    /// 更新批次匹配数量
    pub async fn update_batch_matches(&self, batch_id: &str, total_matches: i32) {
        let mut batches = self.batches.write().await;
        if let Some(batch) = batches.get_mut(batch_id) {
            batch.total_matches = total_matches;
            batch.update_time = Utc::now().to_rfc3339();
        }
    }

    /// 添加匹配结果
    pub async fn add_matches(&self, batch_id: &str, new_matches: Vec<SentenceMatch>) {
        let mut matches = self.matches.write().await;
        let batch_matches = matches.entry(batch_id.to_string()).or_insert_with(Vec::new);
        batch_matches.extend(new_matches);
        
        // 更新批次的匹配数量
        self.update_batch_matches(batch_id, batch_matches.len() as i32).await;
    }

    /// 获取批次的匹配结果
    pub async fn get_matches(&self, batch_id: &str, page_no: i32, page_size: i32) -> (Vec<SentenceMatch>, i32) {
        let matches = self.matches.read().await;
        if let Some(batch_matches) = matches.get(batch_id) {
            let total = batch_matches.len() as i32;
            let start = (page_no * page_size) as usize;
            let end = ((page_no + 1) * page_size) as usize;
            
            let page_matches = if start < batch_matches.len() {
                batch_matches[start..end.min(batch_matches.len())].to_vec()
            } else {
                Vec::new()
            };
            
            (page_matches, total)
        } else {
            (Vec::new(), 0)
        }
    }

    /// 获取批次列表
    pub async fn get_batch_list(
        &self,
        page_no: i32,
        page_size: i32,
        status_filter: Option<&str>,
        keyword: Option<&str>,
    ) -> (Vec<PlagiarismBatch>, i32) {
        let batches = self.batches.read().await;
        let mut filtered_batches: Vec<PlagiarismBatch> = batches
            .values()
            .filter(|batch| {
                // 状态筛选
                if let Some(status) = status_filter {
                    if batch.status != status {
                        return false;
                    }
                }
                
                // 关键词筛选
                if let Some(keyword) = keyword {
                    let keyword_lower = keyword.to_lowercase();
                    if !batch.name.to_lowercase().contains(&keyword_lower) 
                        && !batch.description.as_ref().unwrap_or(&String::new()).to_lowercase().contains(&keyword_lower) {
                        return false;
                    }
                }
                
                true
            })
            .cloned()
            .collect();

        // 按创建时间降序排序
        filtered_batches.sort_by(|a, b| b.create_time.cmp(&a.create_time));
        
        let total = filtered_batches.len() as i32;
        let start = (page_no * page_size) as usize;
        let end = ((page_no + 1) * page_size) as usize;
        
        let page_batches = if start < filtered_batches.len() {
            filtered_batches[start..end.min(filtered_batches.len())].to_vec()
        } else {
            Vec::new()
        };
        
        (page_batches, total)
    }

    /// 删除批次
    pub async fn delete_batch(&self, batch_id: &str) -> bool {
        // 检查是否正在运行
        {
            let running_tasks = self.running_tasks.lock().unwrap();
            if running_tasks.get(batch_id).unwrap_or(&false) == &true {
                return false; // 正在运行的任务不能删除
            }
        }

        let mut batches = self.batches.write().await;
        let mut matches = self.matches.write().await;
        
        batches.remove(batch_id).is_some() | matches.remove(batch_id).is_some()
    }

    /// 取消正在运行的任务
    pub async fn cancel_task(&self, batch_id: &str) -> bool {
        {
            let mut running_tasks = self.running_tasks.lock().unwrap();
            running_tasks.insert(batch_id.to_string(), false); // 标记为取消
        }
        
        self.update_batch_status(batch_id, "failed", None).await;
        true
    }

    /// 标记任务开始
    pub fn mark_task_started(&self, batch_id: &str) {
        let mut running_tasks = self.running_tasks.lock().unwrap();
        running_tasks.insert(batch_id.to_string(), true);
    }

    /// 标记任务完成
    pub fn mark_task_completed(&self, batch_id: &str) {
        let mut running_tasks = self.running_tasks.lock().unwrap();
        running_tasks.remove(batch_id);
    }

    /// 检查任务是否被取消
    pub fn is_task_cancelled(&self, batch_id: &str) -> bool {
        let running_tasks = self.running_tasks.lock().unwrap();
        running_tasks.get(batch_id).unwrap_or(&true) == &false
    }

    /// 获取批次统计信息
    pub async fn get_batch_statistics(&self, batch_id: &str) -> Option<BatchStatistics> {
        let batch = self.get_batch(batch_id).await?;
        let matches = self.matches.read().await;
        let batch_matches = matches.get(batch_id)?;

        let total_matches = batch_matches.len() as i32;
        let exact_matches = batch_matches.iter().filter(|m| m.match_type == "exact").count() as i32;
        let similar_matches = batch_matches.iter().filter(|m| m.match_type == "similar").count() as i32;
        let partial_matches = batch_matches.iter().filter(|m| m.match_type == "partial").count() as i32;

        let average_similarity = if total_matches > 0 {
            batch_matches.iter().map(|m| m.similarity).sum::<f64>() / total_matches as f64
        } else {
            0.0
        };

        let high_similarity_matches = batch_matches.iter().filter(|m| m.similarity > 0.8).count() as i32;
        let medium_similarity_matches = batch_matches.iter().filter(|m| m.similarity >= 0.5 && m.similarity <= 0.8).count() as i32;
        let low_similarity_matches = batch_matches.iter().filter(|m| m.similarity < 0.5).count() as i32;

        Some(BatchStatistics {
            batch_id: batch_id.to_string(),
            total_books: batch.book_ids.len() as i32,
            total_sentences: 0, // 这里需要根据实际情况计算
            total_matches,
            exact_matches,
            similar_matches,
            partial_matches,
            average_similarity,
            high_similarity_matches,
            medium_similarity_matches,
            low_similarity_matches,
        })
    }

    /// 生成批次ID
    fn generate_batch_id(&self) -> String {
        format!("batch_{}", Utc::now().timestamp_millis())
    }
}

impl Default for BatchManager {
    fn default() -> Self {
        Self::new()
    }
}

// 全局批次管理器实例
use lazy_static::lazy_static;

lazy_static! {
    pub static ref GLOBAL_BATCH_MANAGER: BatchManager = BatchManager::new();
}
