use super::{<PERSON><PERSON><PERSON>Extractor, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BatchManager};
use crate::commands::plagiarism::{PlagiarismCompareParams, SentenceMatch};
use std::collections::HashMap;
use tokio::task;
use chrono::Utc;

pub struct PlagiarismDetector {
    sentence_extractor: SentenceExtractor,
    similarity_calculator: SimilarityCalculator,
    batch_manager: BatchManager,
}

impl PlagiarismDetector {
    pub fn new() -> Self {
        Self {
            sentence_extractor: SentenceExtractor::new(10), // 最小句子长度10个字符
            similarity_calculator: SimilarityCalculator::default(),
            batch_manager: BatchManager::new(),
        }
    }

    /// 开始查重对比
    pub async fn start_comparison(&self, params: PlagiarismCompareParams) -> Result<String, String> {
        // 创建批次
        let batch_id = self.batch_manager.create_batch(
            params.batch_name.clone(),
            params.description.clone(),
            params.book_ids.clone(),
        ).await;

        // 更新相似度计算器的阈值
        let mut calculator = self.similarity_calculator.clone();
        calculator.set_thresholds(
            0.95, // 精确匹配阈值
            params.similarity_threshold,
            params.similarity_threshold * 0.7, // 部分匹配阈值设为相似匹配阈值的70%
        );

        // 在后台启动查重任务
        let batch_manager = self.batch_manager.clone();
        let sentence_extractor = self.sentence_extractor.clone();
        let book_ids = params.book_ids.clone();
        let min_sentence_length = params.min_sentence_length;
        let enable_exact_match = params.enable_exact_match;
        let enable_similar_match = params.enable_similar_match;
        let enable_partial_match = params.enable_partial_match;
        let batch_id_clone = batch_id.clone();

        task::spawn(async move {
            if let Err(e) = Self::run_comparison_task(
                batch_manager,
                sentence_extractor,
                calculator,
                batch_id_clone,
                book_ids,
                min_sentence_length,
                enable_exact_match,
                enable_similar_match,
                enable_partial_match,
            ).await {
                eprintln!("查重任务失败: {}", e);
            }
        });

        Ok(batch_id)
    }

    /// 运行查重任务
    async fn run_comparison_task(
        batch_manager: BatchManager,
        sentence_extractor: SentenceExtractor,
        similarity_calculator: SimilarityCalculator,
        batch_id: String,
        book_ids: Vec<String>,
        min_sentence_length: i32,
        enable_exact_match: bool,
        enable_similar_match: bool,
        enable_partial_match: bool,
    ) -> Result<(), String> {
        // 标记任务开始
        batch_manager.mark_task_started(&batch_id);
        batch_manager.update_batch_status(&batch_id, "processing", Some(0)).await;

        // 模拟获取书籍内容（实际应该从数据库或文件系统获取）
        let books_content = Self::get_books_content(&book_ids).await?;

        // 检查任务是否被取消
        if batch_manager.is_task_cancelled(&batch_id) {
            batch_manager.update_batch_status(&batch_id, "failed", None).await;
            batch_manager.mark_task_completed(&batch_id);
            return Err("任务被取消".to_string());
        }

        // 提取所有书籍的句子
        batch_manager.update_batch_status(&batch_id, "processing", Some(20)).await;
        let all_sentences = sentence_extractor.extract_sentences_from_books(&books_content);

        // 检查任务是否被取消
        if batch_manager.is_task_cancelled(&batch_id) {
            batch_manager.update_batch_status(&batch_id, "failed", None).await;
            batch_manager.mark_task_completed(&batch_id);
            return Err("任务被取消".to_string());
        }

        // 进行句子对比
        batch_manager.update_batch_status(&batch_id, "processing", Some(40)).await;
        let matches = Self::compare_sentences(
            &all_sentences,
            &similarity_calculator,
            min_sentence_length,
            enable_exact_match,
            enable_similar_match,
            enable_partial_match,
            &batch_manager,
            &batch_id,
        ).await?;

        // 保存匹配结果
        batch_manager.update_batch_status(&batch_id, "processing", Some(80)).await;
        batch_manager.add_matches(&batch_id, matches).await;

        // 完成任务
        batch_manager.update_batch_status(&batch_id, "completed", Some(100)).await;
        batch_manager.mark_task_completed(&batch_id);

        Ok(())
    }

    /// 模拟获取书籍内容
    async fn get_books_content(book_ids: &[String]) -> Result<HashMap<String, (String, String)>, String> {
        let mut books = HashMap::new();
        
        // 这里应该从实际的数据源获取书籍内容
        // 目前使用模拟数据
        for book_id in book_ids {
            let book_name = format!("书籍{}", book_id);
            let content = format!(
                "这是{}的内容。它包含了很多有趣的句子。\
                每个句子都有其独特的含义和价值。\
                我们可以通过分析这些句子来发现相似性。\
                相似的句子可能表明存在抄袭或引用关系。\
                这个系统可以帮助检测文本之间的相似性。\
                通过比较不同书籍的句子，我们可以找到重复或相似的内容。\
                这对于学术诚信和版权保护非常重要。",
                book_name
            );
            books.insert(book_id.clone(), (book_name, content));
        }
        
        Ok(books)
    }

    /// 比较句子并找出匹配
    async fn compare_sentences(
        all_sentences: &HashMap<String, Vec<super::sentence_extractor::Sentence>>,
        similarity_calculator: &SimilarityCalculator,
        min_sentence_length: i32,
        enable_exact_match: bool,
        enable_similar_match: bool,
        enable_partial_match: bool,
        batch_manager: &BatchManager,
        batch_id: &str,
    ) -> Result<Vec<SentenceMatch>, String> {
        let mut matches = Vec::new();
        let book_ids: Vec<String> = all_sentences.keys().cloned().collect();
        let total_comparisons = book_ids.len() * (book_ids.len() - 1) / 2;
        let mut completed_comparisons = 0;

        // 比较每对书籍
        for i in 0..book_ids.len() {
            for j in (i + 1)..book_ids.len() {
                // 检查任务是否被取消
                if batch_manager.is_task_cancelled(batch_id) {
                    return Err("任务被取消".to_string());
                }

                let book1_id = &book_ids[i];
                let book2_id = &book_ids[j];
                
                if let (Some(sentences1), Some(sentences2)) = (
                    all_sentences.get(book1_id),
                    all_sentences.get(book2_id),
                ) {
                    // 比较两本书的句子
                    for sentence1 in sentences1 {
                        if sentence1.content.len() < min_sentence_length as usize {
                            continue;
                        }

                        for sentence2 in sentences2 {
                            if sentence2.content.len() < min_sentence_length as usize {
                                continue;
                            }

                            if let Some(similarity_match) = similarity_calculator.calculate_similarity(
                                &sentence1.content,
                                &sentence2.content,
                            ) {
                                // 根据启用的匹配类型过滤结果
                                let should_include = match similarity_match.match_type.as_str() {
                                    "exact" => enable_exact_match,
                                    "similar" => enable_similar_match,
                                    "partial" => enable_partial_match,
                                    _ => false,
                                };

                                if should_include {
                                    matches.push(SentenceMatch {
                                        id: format!("match_{}_{}", matches.len(), Utc::now().timestamp_millis()),
                                        source_book_id: sentence1.book_id.clone(),
                                        source_book_name: sentence1.book_name.clone(),
                                        source_page: sentence1.page,
                                        source_content: sentence1.content.clone(),
                                        target_book_id: sentence2.book_id.clone(),
                                        target_book_name: sentence2.book_name.clone(),
                                        target_page: sentence2.page,
                                        target_content: sentence2.content.clone(),
                                        similarity: similarity_match.similarity,
                                        match_type: similarity_match.match_type.as_str().to_string(),
                                        create_time: Utc::now().to_rfc3339(),
                                    });
                                }
                            }
                        }
                    }
                }

                completed_comparisons += 1;
                let progress = 40 + (completed_comparisons * 40 / total_comparisons);
                batch_manager.update_batch_status(batch_id, "processing", Some(progress as i32)).await;
            }
        }

        // 按相似度降序排序
        matches.sort_by(|a, b| b.similarity.partial_cmp(&a.similarity).unwrap());

        Ok(matches)
    }

    /// 获取批次管理器的引用
    pub fn get_batch_manager(&self) -> &BatchManager {
        &self.batch_manager
    }
}

impl Default for PlagiarismDetector {
    fn default() -> Self {
        Self::new()
    }
}

// 全局查重检测器实例
use lazy_static::lazy_static;

lazy_static! {
    pub static ref GLOBAL_PLAGIARISM_DETECTOR: PlagiarismDetector = PlagiarismDetector::new();
}
