import React, { useState } from 'react';
import {
    Float<PERSON><PERSON>on,
    Drawer,
    List,
    Button,
    Typography,
    Space,
    Card,
    Tag,
    message,
    Popconfirm,
    Empty,
    Input,
    Form,
    InputNumber,
    Switch,
    Row,
    Col
} from 'antd';
import {
    BookOutlined,
    DeleteOutlined,
    ClearOutlined,
    PlayCircleOutlined,
    SettingOutlined
} from '@ant-design/icons';
import { usePlagiarism } from '../../../context/PlagiarismContext.tsx';
import { invokePlagiarismCompare } from '../../../invoker/plagiarism.ts';
import { PlagiarismCompareParams } from '../../../interface/plagiarism.ts';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;
const { TextArea } = Input;

function PlagiarismFloatButton() {
    const {
        selectedBooks,
        removeBook,
        clearBooks,
        getSelectedCount
    } = usePlagiarism();

    const navigate = useNavigate();

    const [drawerVisible, setDrawerVisible] = useState(false);
    const [settingsVisible, setSettingsVisible] = useState(false);
    const [isComparing, setIsComparing] = useState(false);
    const [form] = Form.useForm();

    // 默认设置
    const defaultSettings = {
        batchName: '',
        description: '',
        similarityThreshold: 0.7,
        minSentenceLength: 10,
        enableExactMatch: true,
        enableSimilarMatch: true,
        enablePartialMatch: false,
    };

    const handleOpenDrawer = () => {
        setDrawerVisible(true);
    };

    const handleCloseDrawer = () => {
        setDrawerVisible(false);
        setSettingsVisible(false);
    };

    const handleRemoveBook = (bookId: string) => {
        removeBook(bookId);
        message.success('已移除书籍');
    };

    const handleClearAll = () => {
        clearBooks();
        message.success('已清空所有书籍');
    };

    const handleStartComparison = () => {
        if (selectedBooks.length < 2) {
            message.error('请至少选择两本书进行对比');
            return;
        }
        setSettingsVisible(true);
        
        // 生成默认批次名称
        const now = new Date();
        const timestamp = now.toLocaleString('zh-CN').replace(/[\/\s:]/g, '');
        form.setFieldsValue({
            ...defaultSettings,
            batchName: `查重批次_${timestamp}`,
        });
    };

    const handleSubmitComparison = async (values: any) => {
        setIsComparing(true);
        try {
            const params: PlagiarismCompareParams = {
                bookIds: selectedBooks.map(book => book.id),
                batchName: values.batchName,
                description: values.description,
                similarityThreshold: values.similarityThreshold,
                minSentenceLength: values.minSentenceLength,
                enableExactMatch: values.enableExactMatch,
                enableSimilarMatch: values.enableSimilarMatch,
                enablePartialMatch: values.enablePartialMatch,
            };

            await invokePlagiarismCompare(params);
            message.success('查重任务已开始，正在跳转到查重对比页面');

            // 清空选中的书籍
            clearBooks();
            handleCloseDrawer();

            // 导航到查重页面
            navigate('/plagiarism');
        } catch (error: any) {
            message.error(`启动查重失败：${error.message}`);
        } finally {
            setIsComparing(false);
        }
    };

    const selectedCount = getSelectedCount();

    if (selectedCount === 0) {
        return null; // 没有选中书籍时不显示浮动按钮
    }

    return (
        <>
            <FloatButton
                icon={<BookOutlined />}
                type="primary"
                style={{ right: 24, bottom: 24 }}
                badge={{ count: selectedCount, color: '#52c41a' }}
                onClick={handleOpenDrawer}
                tooltip="管理查重书籍"
            />

            <Drawer
                title={
                    <Space>
                        <BookOutlined />
                        <span>查重书籍管理</span>
                        <Tag color="blue">{selectedCount} 本书籍</Tag>
                    </Space>
                }
                placement="right"
                width={600}
                open={drawerVisible}
                onClose={handleCloseDrawer}
                extra={
                    <Space>
                        <Popconfirm
                            title="确定要清空所有书籍吗？"
                            onConfirm={handleClearAll}
                            disabled={selectedCount === 0}
                        >
                            <Button 
                                icon={<ClearOutlined />} 
                                disabled={selectedCount === 0}
                            >
                                清空
                            </Button>
                        </Popconfirm>
                        <Button
                            type="primary"
                            icon={<PlayCircleOutlined />}
                            onClick={handleStartComparison}
                            disabled={selectedCount < 2}
                        >
                            开始查重
                        </Button>
                    </Space>
                }
            >
                {!settingsVisible ? (
                    <div>
                        {selectedBooks.length === 0 ? (
                            <Empty description="暂无选中的书籍" />
                        ) : (
                            <>
                                <div style={{ marginBottom: 16 }}>
                                    <Text type="secondary">
                                        已选择 {selectedCount} 本书籍，
                                        {selectedCount >= 2 ? '可以开始查重对比' : '至少需要选择2本书籍才能进行查重'}
                                    </Text>
                                </div>
                                
                                <List
                                    dataSource={selectedBooks}
                                    renderItem={(book) => (
                                        <List.Item
                                            actions={[
                                                <Button
                                                    type="text"
                                                    danger
                                                    icon={<DeleteOutlined />}
                                                    onClick={() => handleRemoveBook(book.id)}
                                                >
                                                    移除
                                                </Button>
                                            ]}
                                        >
                                            <List.Item.Meta
                                                avatar={
                                                    book.coverImageUrl ? (
                                                        <img
                                                            src={book.coverImageUrl}
                                                            alt={book.bookName}
                                                            style={{ width: 48, height: 64, objectFit: 'cover' }}
                                                        />
                                                    ) : (
                                                        <div
                                                            style={{
                                                                width: 48,
                                                                height: 64,
                                                                backgroundColor: '#f0f0f0',
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                fontSize: '12px',
                                                                textAlign: 'center'
                                                            }}
                                                        >
                                                            <BookOutlined />
                                                        </div>
                                                    )
                                                }
                                                title={<strong>《{book.bookName}》</strong>}
                                                description={
                                                    <div>
                                                        {book.author && <div>作者：{book.author}</div>}
                                                        {book.type && <Tag>{book.type}</Tag>}
                                                    </div>
                                                }
                                            />
                                        </List.Item>
                                    )}
                                />
                            </>
                        )}
                    </div>
                ) : (
                    <div>
                        <Card 
                            title={
                                <Space>
                                    <SettingOutlined />
                                    <span>查重设置</span>
                                </Space>
                            }
                            extra={
                                <Button onClick={() => setSettingsVisible(false)}>
                                    返回
                                </Button>
                            }
                        >
                            <Form
                                form={form}
                                layout="vertical"
                                onFinish={handleSubmitComparison}
                                initialValues={defaultSettings}
                            >
                                <Form.Item
                                    label="批次名称"
                                    name="batchName"
                                    rules={[{ required: true, message: '请输入批次名称' }]}
                                >
                                    <Input placeholder="请输入批次名称" />
                                </Form.Item>

                                <Form.Item
                                    label="批次描述"
                                    name="description"
                                >
                                    <TextArea 
                                        placeholder="请输入批次描述（可选）" 
                                        rows={3}
                                    />
                                </Form.Item>

                                <Row gutter={16}>
                                    <Col span={12}>
                                        <Form.Item
                                            label="相似度阈值"
                                            name="similarityThreshold"
                                            rules={[{ required: true, message: '请设置相似度阈值' }]}
                                        >
                                            <InputNumber
                                                min={0.1}
                                                max={1.0}
                                                step={0.1}
                                                style={{ width: '100%' }}
                                                formatter={value => `${(Number(value) * 100).toFixed(0)}%`}
                                                parser={value => Number(value!.replace('%', '')) / 100}
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span={12}>
                                        <Form.Item
                                            label="最小句子长度"
                                            name="minSentenceLength"
                                            rules={[{ required: true, message: '请设置最小句子长度' }]}
                                        >
                                            <InputNumber
                                                min={5}
                                                max={100}
                                                style={{ width: '100%' }}
                                                addonAfter="字符"
                                            />
                                        </Form.Item>
                                    </Col>
                                </Row>

                                <Form.Item label="匹配类型">
                                    <Space direction="vertical">
                                        <Form.Item name="enableExactMatch" valuePropName="checked" noStyle>
                                            <Switch size="small" />
                                            <span style={{ marginLeft: 8 }}>精确匹配（完全相同的句子）</span>
                                        </Form.Item>
                                        <Form.Item name="enableSimilarMatch" valuePropName="checked" noStyle>
                                            <Switch size="small" />
                                            <span style={{ marginLeft: 8 }}>相似匹配（基于语义相似度）</span>
                                        </Form.Item>
                                        <Form.Item name="enablePartialMatch" valuePropName="checked" noStyle>
                                            <Switch size="small" />
                                            <span style={{ marginLeft: 8 }}>部分匹配（包含关系）</span>
                                        </Form.Item>
                                    </Space>
                                </Form.Item>

                                <Form.Item>
                                    <Space>
                                        <Button
                                            type="primary"
                                            htmlType="submit"
                                            loading={isComparing}
                                            icon={<PlayCircleOutlined />}
                                        >
                                            开始查重
                                        </Button>
                                        <Button onClick={() => setSettingsVisible(false)}>
                                            取消
                                        </Button>
                                    </Space>
                                </Form.Item>
                            </Form>
                        </Card>
                    </div>
                )}
            </Drawer>
        </>
    );
}

export default PlagiarismFloatButton;
