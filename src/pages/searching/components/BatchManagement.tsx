import React, { useState, useEffect } from 'react';
import {
    Table,
    Card,
    Button,
    Space,
    Tag,
    Progress,
    message,
    Popconfirm,
    Input,
    Select,
    Row,
    Col,
    Typography,
    Tooltip,
    Modal
} from 'antd';
import {
    DeleteOutlined,
    StopOutlined,
    EyeOutlined,
    ReloadOutlined,
    SearchOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import {
    PlagiarismBatch,
    BatchListParams,
    BatchListResult,
    BatchStatistics
} from '../../../interface/plagiarism.ts';
import {
    invokeBatchList,
    invokeDeleteBatch,
    invokeCancelComparison,
    invokeBatchStatistics
} from '../../../invoker/plagiarism.ts';

const { Title, Text } = Typography;
const { Option } = Select;

function BatchManagement() {
    const [batches, setBatches] = useState<PlagiarismBatch[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [pageNo, setPageNo] = useState<number>(0);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState<number>(0);
    const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined);
    const [keyword, setKeyword] = useState<string>('');
    const [statisticsModal, setStatisticsModal] = useState<{
        visible: boolean;
        batchId: string;
        statistics: BatchStatistics | null;
    }>({
        visible: false,
        batchId: '',
        statistics: null
    });

    // 获取批次列表
    const fetchBatches = async () => {
        setLoading(true);
        try {
            const params: BatchListParams = {
                pageNo,
                pageSize,
                status: statusFilter as any,
                keyword: keyword.trim() || undefined
            };
            const result: BatchListResult = await invokeBatchList(params);
            setBatches(result.list);
            setTotal(result.totals);
        } catch (error: any) {
            message.error(`获取批次列表失败：${error.message}`);
        } finally {
            setLoading(false);
        }
    };

    // 删除批次
    const handleDeleteBatch = async (batchId: string) => {
        try {
            await invokeDeleteBatch(batchId);
            message.success('批次删除成功');
            fetchBatches();
        } catch (error: any) {
            message.error(`删除批次失败：${error.message}`);
        }
    };

    // 取消查重任务
    const handleCancelComparison = async (batchId: string) => {
        try {
            await invokeCancelComparison(batchId);
            message.success('查重任务已取消');
            fetchBatches();
        } catch (error: any) {
            message.error(`取消任务失败：${error.message}`);
        }
    };

    // 查看统计信息
    const handleViewStatistics = async (batchId: string) => {
        try {
            const statistics = await invokeBatchStatistics(batchId);
            setStatisticsModal({
                visible: true,
                batchId,
                statistics
            });
        } catch (error: any) {
            message.error(`获取统计信息失败：${error.message}`);
        }
    };

    // 状态标签渲染
    const renderStatusTag = (status: string) => {
        const statusConfig = {
            pending: { color: 'default', text: '等待中' },
            processing: { color: 'processing', text: '处理中' },
            completed: { color: 'success', text: '已完成' },
            failed: { color: 'error', text: '失败' }
        };
        const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
        return <Tag color={config.color}>{config.text}</Tag>;
    };

    // 进度条渲染
    const renderProgress = (record: PlagiarismBatch) => {
        if (record.status === 'completed') {
            return <Progress percent={100} size="small" status="success" />;
        } else if (record.status === 'failed') {
            return <Progress percent={record.progress || 0} size="small" status="exception" />;
        } else if (record.status === 'processing') {
            return <Progress percent={record.progress || 0} size="small" status="active" />;
        } else {
            return <Progress percent={0} size="small" />;
        }
    };

    // 表格列定义
    const columns: ColumnsType<PlagiarismBatch> = [
        {
            title: '批次名称',
            dataIndex: 'name',
            key: 'name',
            ellipsis: true,
            render: (text, record) => (
                <div>
                    <div style={{ fontWeight: 'bold' }}>{text}</div>
                    {record.description && (
                        <div style={{ fontSize: '12px', color: '#666' }}>
                            {record.description}
                        </div>
                    )}
                </div>
            )
        },
        {
            title: '书籍数量',
            dataIndex: 'bookIds',
            key: 'bookCount',
            width: 100,
            render: (bookIds: string[]) => bookIds.length
        },
        {
            title: '匹配数量',
            dataIndex: 'totalMatches',
            key: 'totalMatches',
            width: 100,
            render: (count) => count || 0
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: 100,
            render: renderStatusTag
        },
        {
            title: '进度',
            key: 'progress',
            width: 120,
            render: (_, record) => renderProgress(record)
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            width: 150,
            render: (time) => new Date(time).toLocaleString('zh-CN')
        },
        {
            title: '操作',
            key: 'actions',
            width: 200,
            render: (_, record) => (
                <Space size="small">
                    <Tooltip title="查看统计">
                        <Button
                            type="link"
                            size="small"
                            icon={<EyeOutlined />}
                            onClick={() => handleViewStatistics(record.id)}
                            disabled={record.status === 'pending'}
                        />
                    </Tooltip>
                    
                    {record.status === 'processing' && (
                        <Popconfirm
                            title="确定要取消这个查重任务吗？"
                            onConfirm={() => handleCancelComparison(record.id)}
                        >
                            <Tooltip title="取消任务">
                                <Button
                                    type="link"
                                    size="small"
                                    icon={<StopOutlined />}
                                    danger
                                />
                            </Tooltip>
                        </Popconfirm>
                    )}
                    
                    <Popconfirm
                        title="确定要删除这个批次吗？删除后无法恢复。"
                        onConfirm={() => handleDeleteBatch(record.id)}
                    >
                        <Tooltip title="删除批次">
                            <Button
                                type="link"
                                size="small"
                                icon={<DeleteOutlined />}
                                danger
                                disabled={record.status === 'processing'}
                            />
                        </Tooltip>
                    </Popconfirm>
                </Space>
            )
        }
    ];

    useEffect(() => {
        fetchBatches();
    }, [pageNo, pageSize, statusFilter]);

    // 搜索处理
    const handleSearch = () => {
        setPageNo(0);
        fetchBatches();
    };

    return (
        <Card 
            title="批次管理" 
            extra={
                <Button 
                    icon={<ReloadOutlined />} 
                    onClick={fetchBatches}
                    loading={loading}
                >
                    刷新
                </Button>
            }
        >
            {/* 搜索和筛选 */}
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
                <Col span={8}>
                    <Input
                        placeholder="搜索批次名称或描述"
                        value={keyword}
                        onChange={(e) => setKeyword(e.target.value)}
                        onPressEnter={handleSearch}
                        suffix={
                            <Button 
                                type="link" 
                                icon={<SearchOutlined />} 
                                onClick={handleSearch}
                            />
                        }
                    />
                </Col>
                <Col span={6}>
                    <Select
                        placeholder="筛选状态"
                        value={statusFilter}
                        onChange={setStatusFilter}
                        allowClear
                        style={{ width: '100%' }}
                    >
                        <Option value="pending">等待中</Option>
                        <Option value="processing">处理中</Option>
                        <Option value="completed">已完成</Option>
                        <Option value="failed">失败</Option>
                    </Select>
                </Col>
            </Row>

            {/* 批次列表表格 */}
            <Table
                columns={columns}
                dataSource={batches}
                rowKey="id"
                loading={loading}
                pagination={{
                    current: pageNo + 1,
                    pageSize,
                    total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                    onChange: (page, size) => {
                        setPageNo(page - 1);
                        setPageSize(size || 10);
                    }
                }}
            />

            {/* 统计信息模态框 */}
            <Modal
                title="批次统计信息"
                open={statisticsModal.visible}
                onCancel={() => setStatisticsModal({ visible: false, batchId: '', statistics: null })}
                footer={null}
                width={600}
            >
                {statisticsModal.statistics && (
                    <div>
                        <Row gutter={[16, 16]}>
                            <Col span={12}>
                                <Card size="small">
                                    <div style={{ textAlign: 'center' }}>
                                        <Title level={4}>{statisticsModal.statistics.totalBooks}</Title>
                                        <Text>参与书籍</Text>
                                    </div>
                                </Card>
                            </Col>
                            <Col span={12}>
                                <Card size="small">
                                    <div style={{ textAlign: 'center' }}>
                                        <Title level={4}>{statisticsModal.statistics.totalMatches}</Title>
                                        <Text>总匹配数</Text>
                                    </div>
                                </Card>
                            </Col>
                            <Col span={8}>
                                <Card size="small">
                                    <div style={{ textAlign: 'center' }}>
                                        <Title level={5} style={{ color: '#52c41a' }}>
                                            {statisticsModal.statistics.exactMatches}
                                        </Title>
                                        <Text>精确匹配</Text>
                                    </div>
                                </Card>
                            </Col>
                            <Col span={8}>
                                <Card size="small">
                                    <div style={{ textAlign: 'center' }}>
                                        <Title level={5} style={{ color: '#1890ff' }}>
                                            {statisticsModal.statistics.similarMatches}
                                        </Title>
                                        <Text>相似匹配</Text>
                                    </div>
                                </Card>
                            </Col>
                            <Col span={8}>
                                <Card size="small">
                                    <div style={{ textAlign: 'center' }}>
                                        <Title level={5} style={{ color: '#faad14' }}>
                                            {statisticsModal.statistics.partialMatches}
                                        </Title>
                                        <Text>部分匹配</Text>
                                    </div>
                                </Card>
                            </Col>
                            <Col span={24}>
                                <Card size="small">
                                    <div style={{ textAlign: 'center' }}>
                                        <Title level={5}>
                                            {(statisticsModal.statistics.averageSimilarity * 100).toFixed(1)}%
                                        </Title>
                                        <Text>平均相似度</Text>
                                    </div>
                                </Card>
                            </Col>
                        </Row>
                    </div>
                )}
            </Modal>
        </Card>
    );
}

export default BatchManagement;
