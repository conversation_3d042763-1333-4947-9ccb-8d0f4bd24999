import React, { useState, useEffect } from 'react';
import { 
    Card, 
    Button, 
    Select, 
    InputNumber, 
    Switch, 
    Input, 
    message, 
    Tabs, 
    Space,
    Row,
    Col,
    Typography,
    Divider
} from 'antd';
import { PlayCircleOutlined, StopOutlined, SettingOutlined } from '@ant-design/icons';
import { BookSearchResult } from '../../../interface/searching.ts';
import { 
    PlagiarismCompareParams, 
    PlagiarismBatch 
} from '../../../interface/plagiarism.ts';
import { invokePlagiarismCompare } from '../../../invoker/plagiarism.ts';
import BatchManagement from './BatchManagement.tsx';
import PlagiarismResultCard from './PlagiarismResultCard.tsx';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface PlagiarismDetectionProps {
    availableBooks: BookSearchResult[];
    onRefreshBooks: () => void;
}

function PlagiarismDetection({ availableBooks, onRefreshBooks }: PlagiarismDetectionProps) {
    const [selectedBooks, setSelectedBooks] = useState<string[]>([]);
    const [batchName, setBatchName] = useState<string>('');
    const [description, setDescription] = useState<string>('');
    const [similarityThreshold, setSimilarityThreshold] = useState<number>(0.7);
    const [minSentenceLength, setMinSentenceLength] = useState<number>(10);
    const [enableExactMatch, setEnableExactMatch] = useState<boolean>(true);
    const [enableSimilarMatch, setEnableSimilarMatch] = useState<boolean>(true);
    const [enablePartialMatch, setEnablePartialMatch] = useState<boolean>(false);
    const [isComparing, setIsComparing] = useState<boolean>(false);
    const [currentBatch, setCurrentBatch] = useState<PlagiarismBatch | null>(null);
    const [activeTab, setActiveTab] = useState<string>('setup');

    // 开始查重对比
    const handleStartComparison = async () => {
        if (selectedBooks.length < 2) {
            message.error('请至少选择两本书进行对比');
            return;
        }
        
        if (!batchName.trim()) {
            message.error('请输入批次名称');
            return;
        }

        const params: PlagiarismCompareParams = {
            bookIds: selectedBooks,
            batchName: batchName.trim(),
            description: description.trim(),
            similarityThreshold,
            minSentenceLength,
            enableExactMatch,
            enableSimilarMatch,
            enablePartialMatch
        };

        try {
            setIsComparing(true);
            const batch = await invokePlagiarismCompare(params);
            setCurrentBatch(batch);
            message.success('查重任务已开始，请在批次管理中查看进度');
            setActiveTab('batches');
        } catch (error: any) {
            message.error(`启动查重失败：${error.message}`);
        } finally {
            setIsComparing(false);
        }
    };

    // 重置表单
    const handleReset = () => {
        setSelectedBooks([]);
        setBatchName('');
        setDescription('');
        setSimilarityThreshold(0.7);
        setMinSentenceLength(10);
        setEnableExactMatch(true);
        setEnableSimilarMatch(true);
        setEnablePartialMatch(false);
    };

    // 生成默认批次名称
    const generateBatchName = () => {
        const now = new Date();
        const timestamp = now.toLocaleString('zh-CN').replace(/[\/\s:]/g, '');
        setBatchName(`查重批次_${timestamp}`);
    };

    useEffect(() => {
        if (!batchName && selectedBooks.length > 0) {
            generateBatchName();
        }
    }, [selectedBooks]);

    const tabItems = [
        {
            key: 'setup',
            label: '设置查重',
            children: (
                <Card title="查重设置" extra={
                    <Space>
                        <Button onClick={handleReset}>重置</Button>
                        <Button onClick={onRefreshBooks}>刷新书籍列表</Button>
                    </Space>
                }>
                    <Row gutter={[16, 16]}>
                        <Col span={24}>
                            <Title level={5}>选择书籍</Title>
                            <Select
                                mode="multiple"
                                placeholder="请选择要进行查重对比的书籍"
                                style={{ width: '100%' }}
                                value={selectedBooks}
                                onChange={setSelectedBooks}
                                showSearch
                                filterOption={(input, option) =>
                                    (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                                }
                            >
                                {availableBooks.map(book => (
                                    <Option key={book.id} value={book.id}>
                                        《{book.bookName}》 - {book.author || '未知作者'}
                                    </Option>
                                ))}
                            </Select>
                            <Text type="secondary">已选择 {selectedBooks.length} 本书籍</Text>
                        </Col>

                        <Col span={12}>
                            <Title level={5}>批次信息</Title>
                            <Space direction="vertical" style={{ width: '100%' }}>
                                <Input
                                    placeholder="批次名称"
                                    value={batchName}
                                    onChange={(e) => setBatchName(e.target.value)}
                                    suffix={
                                        <Button 
                                            type="link" 
                                            size="small" 
                                            onClick={generateBatchName}
                                        >
                                            自动生成
                                        </Button>
                                    }
                                />
                                <TextArea
                                    placeholder="批次描述（可选）"
                                    value={description}
                                    onChange={(e) => setDescription(e.target.value)}
                                    rows={3}
                                />
                            </Space>
                        </Col>

                        <Col span={12}>
                            <Title level={5}>对比参数</Title>
                            <Space direction="vertical" style={{ width: '100%' }}>
                                <div>
                                    <Text>相似度阈值：</Text>
                                    <InputNumber
                                        min={0.1}
                                        max={1.0}
                                        step={0.1}
                                        value={similarityThreshold}
                                        onChange={(value) => setSimilarityThreshold(value || 0.7)}
                                        style={{ marginLeft: 8 }}
                                    />
                                </div>
                                <div>
                                    <Text>最小句子长度：</Text>
                                    <InputNumber
                                        min={5}
                                        max={100}
                                        value={minSentenceLength}
                                        onChange={(value) => setMinSentenceLength(value || 10)}
                                        style={{ marginLeft: 8 }}
                                    />
                                </div>
                            </Space>
                        </Col>

                        <Col span={24}>
                            <Title level={5}>匹配类型</Title>
                            <Space direction="vertical">
                                <div>
                                    <Switch 
                                        checked={enableExactMatch} 
                                        onChange={setEnableExactMatch} 
                                    />
                                    <Text style={{ marginLeft: 8 }}>精确匹配（完全相同的句子）</Text>
                                </div>
                                <div>
                                    <Switch 
                                        checked={enableSimilarMatch} 
                                        onChange={setEnableSimilarMatch} 
                                    />
                                    <Text style={{ marginLeft: 8 }}>相似匹配（基于语义相似度）</Text>
                                </div>
                                <div>
                                    <Switch 
                                        checked={enablePartialMatch} 
                                        onChange={setEnablePartialMatch} 
                                    />
                                    <Text style={{ marginLeft: 8 }}>部分匹配（包含关系）</Text>
                                </div>
                            </Space>
                        </Col>

                        <Col span={24}>
                            <Divider />
                            <Space>
                                <Button
                                    type="primary"
                                    icon={<PlayCircleOutlined />}
                                    loading={isComparing}
                                    onClick={handleStartComparison}
                                    disabled={selectedBooks.length < 2 || !batchName.trim()}
                                >
                                    开始查重
                                </Button>
                                <Text type="secondary">
                                    {selectedBooks.length < 2 && '请至少选择两本书'}
                                    {selectedBooks.length >= 2 && !batchName.trim() && '请输入批次名称'}
                                    {selectedBooks.length >= 2 && batchName.trim() && '准备就绪'}
                                </Text>
                            </Space>
                        </Col>
                    </Row>
                </Card>
            )
        },
        {
            key: 'batches',
            label: '批次管理',
            children: <BatchManagement />
        },
        {
            key: 'results',
            label: '查重结果',
            children: <PlagiarismResultCard />
        }
    ];

    return (
        <div style={{ padding: '20px' }}>
            <Tabs 
                activeKey={activeTab} 
                onChange={setActiveTab}
                items={tabItems}
            />
        </div>
    );
}

export default PlagiarismDetection;
