import "./App.css";
import {Button, Layout, Menu, theme, ConfigProvider, Typography} from "antd";
import { useEffect, useState } from "react";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SettingOutlined,
  ScheduleOutlined,
  ImportOutlined,
  BookOutlined,
} from "@ant-design/icons";
import packageJson from '../package.json';
import { Outlet, To, useNavigate, useLocation } from "react-router-dom";
import { MathJaxContext } from "better-react-mathjax";
import zhCN from "antd/locale/zh_CN";
import {PublishOutlined} from "@mui/icons-material";
// import Activate from "./pages/activate/Activate.tsx";
// import {invokeActivateStatus, invokeGetUnActivateStatus} from "./invoker/activation.ts";

const showEeaInfo: string = import.meta.env.VITE_SHOW_EEA_INFO;
const eeaName: string = import.meta.env.VITE_EEA_NAME;
const eeaLogoPath: string = import.meta.env.VITE_EEA_LOGO_PATH;

const { Header, Content, Sider } = Layout;
const { Text } = Typography;
function VersionDisplay({ version }: { version: string }) {
  return (
      <div
          style={{
            position: "absolute",
            bottom: 0,
            width: "100%",
            padding: "10px",
            textAlign: "center",
          }}
      >
        <Text style={{ color: "rgba(255, 255, 255, 0.65)" }}>v{version}</Text>
      </div>
  );
}

function App() {
  const [collapsed, setCollapsed] = useState(false);
  const [appVersion, setAppVersion] = useState<string>("-.-.-");
  const navigate = useNavigate();
  const location = useLocation();

  const {
    token: { colorBgContainer },
  } = theme.useToken();

  function navigateTo(e: { key: To }) {
    navigate(e.key);
  }

  const nonLayoutList = ["reader", "letter-dictionary-book", "word-dictionary-book"];

  const nonLayout = nonLayoutList.some((substring) =>
      location.pathname.includes(substring),
  );
  const mathJaxConfig = {
    tex: {
      inlineMath: [
        ["$", "$"],
        ["\\(", "\\)"],
      ],
      displayMath: [
        ["$$", "$$"],
        ["\\[", "\\]"],
      ],
    },
  };

  // const [activateStatus, setActivateStatus] = useState<boolean>(false);
  // const [unActivateInfo, setUnActivateInfo] = useState<string>("");

  // async function getActivateStatus() {
  //   const activateStatus = await invokeActivateStatus();
  //   setActivateStatus(activateStatus);
  //   if (!activateStatus) {
  //     const info = await invokeGetUnActivateStatus();
  //     setUnActivateInfo(info);
  //   }
  //
  //   // todo should remove before release
  //   // setActivateStatus(true)
  // }

  async function handleAppVersion() {
    setAppVersion(packageJson.version);
  }

  useEffect(() => {
    // getActivateStatus().then();
    handleAppVersion().then();
  }, []);

  return (
      <MathJaxContext
          config={mathJaxConfig}
          src={"/mathjax/3.2.2/es5/tex-svg-full.js"}
      >
        <ConfigProvider locale={zhCN}>
          {/*{activateStatus ? (*/}
          <Layout style={{ height: "100vh" }}>
            {!nonLayout ? (
                <>
                  <Sider trigger={null} collapsible collapsed={collapsed}>
                    <div
                        className="logo-vertical"
                        style={{
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                        onClick={() => setCollapsed(!collapsed)}
                    >
                      <img
                          src={"/app_logo.svg"}
                          alt="logo"
                          style={{ height: 30 }}
                      />
                      <div
                          style={{
                            display: collapsed ? "none" : "",
                            color: "#f6f6f6",
                          }}
                      >
                        <div
                            style={{
                              fontSize: 18,
                              marginLeft: 10,
                            }}
                        >
                          预出版查重
                        </div>
                      </div>
                    </div>
                    <Menu
                        onClick={navigateTo}
                        theme="dark"
                        mode="inline"
                        defaultSelectedKeys={["/global-search"]}
                        items={[
                          {
                            key: "/global-search",
                            icon: <ScheduleOutlined />,
                            label: "全文检索",
                          },
                          {
                            key: "/plagiarism",
                            icon: <BookOutlined />,
                            label: "查重对比",
                          },
                          {
                            key: "/settings/increment-update",
                            icon: <ImportOutlined />,
                            label: "增量更新",
                          },
                          {
                            key: "/settings/system-feature",
                            icon: <SettingOutlined />,
                            label: "系统配置",
                          },
                          {
                            key: "/settings/release-note",
                            icon: <PublishOutlined />,
                            label: "发布日志",
                          }
                        ]}
                    ></Menu>
                    <VersionDisplay version={appVersion} />
                  </Sider>
                  <Layout>
                    <Header style={{ padding: 0, background: colorBgContainer }}>
                      <div style={{display: 'flex', justifyContent: 'space-between'}}>
                        <div>
                          <Button
                              type="text"
                              icon={
                                collapsed ? (
                                    <MenuUnfoldOutlined />
                                ) : (
                                    <MenuFoldOutlined />
                                )
                              }
                              onClick={() => setCollapsed(!collapsed)}
                              style={{
                                fontSize: "16px",
                                width: 64,
                                height: 64,
                              }}
                          />
                        </div>
                        {showEeaInfo === 'true' && (
                            <div style={{display: 'flex', alignItems: 'center', paddingRight: '20px'}}>
                              {eeaLogoPath && (
                                  <img
                                      src={eeaLogoPath}
                                      alt="logo"
                                      style={{height: 30}}
                                  />
                              )}
                              <span style={{
                                paddingLeft: '5px',
                                fontSize: '20px',
                                fontWeight: 'bold'
                              }}>{ eeaName }</span>
                            </div>
                        )}
                      </div>
                    </Header>
                    <Content
                        style={{
                          margin: "12px",
                          padding: 12,
                          minHeight: 280,
                          background: colorBgContainer,
                          overflowY: "auto",
                        }}
                    >
                      <Outlet />
                    </Content>
                  </Layout>
                </>
            ) : (
                <Layout>
                  <Content
                      style={{
                        padding: 12,
                        minHeight: 280,
                        background: colorBgContainer,
                        overflowY: "auto",
                      }}
                  >
                    <Outlet />
                  </Content>
                </Layout>
            )}
          </Layout>
          {/*) : (*/}
          {/*  <Activate*/}
          {/*      activateStatus={activateStatus}*/}
          {/*    onActivate={getActivateStatus}*/}
          {/*    unActivateInfo={unActivateInfo}*/}
          {/*  />*/}
          {/*)}*/}
        </ConfigProvider>
      </MathJaxContext>
  );
}

export default App;
